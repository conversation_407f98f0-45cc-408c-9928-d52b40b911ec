use hydro_lang::*;
use akd::{AkdLabel, AkdValue, LookupProof};
use akd::storage::StorageManager;
use akd::storage::memory::AsyncInMemoryDatabase;
use akd::ecvrf::HardCodedAkdVRF;
use akd::directory::{Directory, HistoryParams};
use serde::{Serialize, Deserialize};

type Config = akd::ExperimentalConfiguration<akd::ExampleLabel>;

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsg {
    pub updates: Vec<(AkdLabel, AkdValue)>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsgResponse {
    pub epoch: u64,
    pub root_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsgResponse {
    pub lookup_proof: LookupProof,
    pub epoch_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct HistoryMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct HistoryMsgResponse {
    pub history_proof: Vec<u8>, // Serialized HistoryProof
    pub epoch_hash: Vec<u8>,
}

pub struct Server {}

pub struct Clients {}

// API
    // Publish: publish client ID, public key
    // - label: AkdLabel (client identifier)
    // - value: AkdValue (public key data)
    // - epoch: u64 (optional version number)

    // Lookup: verify mapping
    // - label: AkdLabel (client identifier to lookup)
    // - epoch: u64 (specific version, or latest if not specified)
    // - Returns: LookupProof for verification

    // History: verify history of mappings
    // - label: AkdLabel (client identifier)
    // - history_params: HistoryParams (epoch range, limit)
    // - Returns: Vec<HistoryProof> with historical values

pub fn akd_server<'a>(flow: &FlowBuilder<'a>) -> (Process<'a, Server>, Cluster<'a, Clients>) {
    let clients = flow.cluster::<Clients>();
    let server = flow.process::<Server>();
    let _server_tick = server.tick();

    // Use a simpler approach: initialize AKD within each request handler
    // This ensures we have a fresh AKD instance for each operation

    // Client publish requests
    let client_publish_req = clients
        .source_iter(q!([PublishMsg {
            updates: vec![(
                AkdLabel::from(format!("{}", CLUSTER_SELF_ID.raw_id).as_str()),
                AkdValue::from("test_value"),
            )],
        }]))
        .send_bincode(&server);

    // Client lookup requests
    let client_lookup_req = clients
        .source_iter(q!([LookupMsg {
            label: AkdLabel::from(format!("{}", CLUSTER_SELF_ID.raw_id).as_str()),
        }]))
        .send_bincode(&server);

    // Client history requests
    let client_history_req = clients
        .source_iter(q!([HistoryMsg {
            label: AkdLabel::from(format!("{}", CLUSTER_SELF_ID.raw_id).as_str()),
        }]))
        .send_bincode(&server);

    // Process publish requests with async AKD initialization
    let publish_responses = client_publish_req
        .map(q!(|(client_id, msg)| async move {
            // Initialize AKD directory for this request
            let db = AsyncInMemoryDatabase::new();
            let storage_manager = StorageManager::new_no_cache(db);
            let vrf = HardCodedAkdVRF{};

            let akd = Directory::<Config, _, _>::new(storage_manager, vrf)
                .await
                .expect("Could not create AKD directory");

            println!("Processing publish request from client #{}: {:?}", client_id, msg);

            // Perform the actual publish operation
            match akd.publish(msg.updates).await {
                Ok(epoch_hash) => {
                    println!("Published successfully: epoch {}, hash: {:?}",
                            epoch_hash.epoch(), epoch_hash.hash());
                    Some((client_id, PublishMsgResponse {
                        epoch: epoch_hash.epoch(),
                        root_hash: epoch_hash.hash().to_vec(),
                    }))
                },
                Err(e) => {
                    println!("Publish failed: {:?}", e);
                    None
                }
            }
        }))
        .resolve_futures();

    // Send publish responses back to clients
    publish_responses
        .filter_map(q!(|response_opt| response_opt))
        .for_each(q!(|(client_id, response)| {
            println!("Sent publish response to client #{}: {:?}", client_id, response);
        }));

    // Process lookup requests with async AKD initialization
    let lookup_responses = client_lookup_req
        .map(q!(|(client_id, msg)| async move {
            // Initialize AKD directory for this request
            let db = AsyncInMemoryDatabase::new();
            let storage_manager = StorageManager::new_no_cache(db);
            let vrf = HardCodedAkdVRF{};

            let akd = Directory::<Config, _, _>::new(storage_manager, vrf)
                .await
                .expect("Could not create AKD directory");

            println!("Processing lookup request from client #{}: {:?}", client_id, msg);

            match akd.lookup(msg.label).await {
                Ok((lookup_proof, epoch_hash)) => {
                    println!("Lookup successful for client #{}", client_id);
                    Some((client_id, LookupMsgResponse {
                        lookup_proof,
                        epoch_hash: epoch_hash.hash().to_vec(),
                    }))
                },
                Err(e) => {
                    println!("Lookup failed: {:?}", e);
                    None
                }
            }
        }))
        .resolve_futures();

    // Send lookup responses back to clients
    lookup_responses
        .filter_map(q!(|response_opt| response_opt))
        .for_each(q!(|(client_id, response)| {
            println!("Sent lookup response to client #{}: {:?}", client_id, response);
        }));

    // Process history requests with async AKD initialization
    let history_responses = client_history_req
        .map(q!(|(client_id, msg)| async move {
            // Initialize AKD directory for this request
            let db = AsyncInMemoryDatabase::new();
            let storage_manager = StorageManager::new_no_cache(db);
            let vrf = HardCodedAkdVRF{};

            let akd = Directory::<Config, _, _>::new(storage_manager, vrf)
                .await
                .expect("Could not create AKD directory");

            println!("Processing history request from client #{}: {:?}", client_id, msg);

            match akd.key_history(&msg.label, HistoryParams::Complete).await {
                Ok((history_proof, epoch_hash)) => {
                    println!("History lookup successful for client #{}", client_id);
                    // Serialize the history proof for transmission
                    let serialized_proof = bincode::serialize(&history_proof)
                        .unwrap_or_else(|_| vec![]);
                    Some((client_id, HistoryMsgResponse {
                        history_proof: serialized_proof,
                        epoch_hash: epoch_hash.hash().to_vec(),
                    }))
                },
                Err(e) => {
                    println!("History lookup failed: {:?}", e);
                    None
                }
            }
        }))
        .resolve_futures();

    // Send history responses back to clients
    history_responses
        .filter_map(q!(|response_opt| response_opt))
        .for_each(q!(|(client_id, response)| {
            println!("Sent history response to client #{}: {:?}", client_id, response);
        }));

    (server, clients)
}