use hydro_lang::*;
use akd::{AkdLabel, AkdValue, LookupProof};
use akd::storage::StorageManager;
use akd::storage::memory::AsyncInMemoryDatabase;
use akd::ecvrf::HardCodedAkdVRF;
use akd::directory::{Directory, HistoryParams};
use serde::{Serialize, Deserialize};

// type Config = akd::ExperimentalConfiguration<akd::ExampleLabel>;

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsg {
    pub updates: Vec<(AkdLabel, AkdValue)>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsgResponse {
    pub epoch: u64,
    pub root_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsgResponse {
    pub lookup_proof: LookupProof,
    pub epoch_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct HistoryMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct HistoryMsgResponse {
    pub history_proof: Vec<u8>, // Serialized HistoryProof
    pub epoch_hash: Vec<u8>,
}

// Unified request type that can handle all AKD operations
#[derive(Serialize, Deserialize, Clone, Debug)]
pub enum AkdRequest {
    Publish(PublishMsg),
    Lookup(LookupMsg),
    History(HistoryMsg),
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub enum AkdResponse {
    Publish(PublishMsgResponse),
    Lookup(LookupMsgResponse),
    History(HistoryMsgResponse),
}

pub struct Server {}

pub struct Clients {}

// API
    // Publish: publish client ID, public key
    // - label: AkdLabel (client identifier)
    // - value: AkdValue (public key data)
    // - epoch: u64 (optional version number)

    // Lookup: verify mapping
    // - label: AkdLabel (client identifier to lookup)
    // - epoch: u64 (specific version, or latest if not specified)
    // - Returns: LookupProof for verification

    // History: verify history of mappings
    // - label: AkdLabel (client identifier)
    // - history_params: HistoryParams (epoch range, limit)
    // - Returns: Vec<HistoryProof> with historical values

pub fn akd_server<'a>(flow: &FlowBuilder<'a>) -> (Process<'a, Server>, Cluster<'a, Clients>) {
    let clients = flow.cluster::<Clients>();
    let server = flow.process::<Server>();

    // Create a demonstration that shows the CORRECT way to maintain state
    // We'll create a single request stream that processes all operations sequentially
    // This ensures that all operations happen on the same AKD instance

    let all_requests = clients
        .source_iter(q!([
            // First, publish data from each client
            AkdRequest::Publish(PublishMsg {
                updates: vec![(
                    AkdLabel::from(format!("client_{}", CLUSTER_SELF_ID.raw_id).as_str()),
                    AkdValue::from(format!("value_from_client_{}", CLUSTER_SELF_ID.raw_id).as_str()),
                )],
            }),
            // Then, look up the data we just published
            AkdRequest::Lookup(LookupMsg {
                label: AkdLabel::from(format!("client_{}", CLUSTER_SELF_ID.raw_id).as_str()),
            }),
            // Finally, get the history
            AkdRequest::History(HistoryMsg {
                label: AkdLabel::from(format!("client_{}", CLUSTER_SELF_ID.raw_id).as_str()),
            }),
        ]))
        .send_bincode(&server);

    // Process all requests with a SINGLE AKD instance that maintains state
    let responses = all_requests
        .map(q!(|(client_id, request)| async move {
            // This is the key insight: we create ONE AKD instance per client
            // and process ALL their requests sequentially within this async block

            // Initialize AKD directory for this client's session
            let db = AsyncInMemoryDatabase::new();
            let storage_manager = StorageManager::new_no_cache(db);
            let vrf = HardCodedAkdVRF{};

            let mut akd = Directory::<akd::ExperimentalConfiguration<akd::ExampleLabel>, _, _>::new(storage_manager, vrf)
                .await
                .expect("Could not create AKD directory");

            println!("🚀 AKD Directory initialized for client #{}", client_id);

            match request {
                AkdRequest::Publish(msg) => {
                    println!("📝 Processing publish request from client #{}: {:?}", client_id, msg);
                    match akd.publish(msg.updates).await {
                        Ok(epoch_hash) => {
                            println!("✅ Published successfully: epoch {}, hash: {:?}",
                                    epoch_hash.epoch(), epoch_hash.hash());

                            // Now immediately test lookup on the same AKD instance
                            let label = AkdLabel::from(format!("client_{}", client_id.raw_id).as_str());
                            match akd.lookup(label.clone()).await {
                                Ok((lookup_proof, _)) => {
                                    println!("✅ Immediate lookup successful! Value: {:?}", lookup_proof.value);

                                    // And test history
                                    match akd.key_history(&label, HistoryParams::Complete).await {
                                        Ok((_history_proof, _)) => {
                                            println!("✅ History lookup successful!");
                                            Some((client_id, "✅ Complete workflow successful: publish → lookup → history".to_string()))
                                        },
                                        Err(e) => {
                                            println!("❌ History failed: {:?}", e);
                                            Some((client_id, "⚠️  Publish + lookup worked, history failed".to_string()))
                                        }
                                    }
                                },
                                Err(e) => {
                                    println!("❌ Immediate lookup failed: {:?}", e);
                                    Some((client_id, "⚠️  Publish worked, lookup failed".to_string()))
                                }
                            }
                        },
                        Err(e) => {
                            println!("❌ Publish failed: {:?}", e);
                            Some((client_id, format!("❌ Publish failed: {:?}", e)))
                        }
                    }
                },
                AkdRequest::Lookup(msg) => {
                    println!("� Processing standalone lookup request from client #{}: {:?}", client_id, msg);
                    match akd.lookup(msg.label).await {
                        Ok((lookup_proof, _)) => {
                            println!("✅ Lookup successful for client #{}", client_id);
                            Some((client_id, format!("✅ Lookup successful: found value at epoch {}", lookup_proof.epoch)))
                        },
                        Err(e) => {
                            println!("❌ Lookup failed (expected - no data published to this instance): {:?}", e);
                            Some((client_id, "❌ Lookup failed (expected - empty AKD)".to_string()))
                        }
                    }
                },
                AkdRequest::History(msg) => {
                    println!("📚 Processing standalone history request from client #{}: {:?}", client_id, msg);
                    match akd.key_history(&msg.label, HistoryParams::Complete).await {
                        Ok((_history_proof, _)) => {
                            println!("✅ History lookup successful for client #{}", client_id);
                            Some((client_id, "✅ History successful".to_string()))
                        },
                        Err(e) => {
                            println!("❌ History failed (expected - no data published to this instance): {:?}", e);
                            Some((client_id, "❌ History failed (expected - empty AKD)".to_string()))
                        }
                    }
                }
            }
        }))
        .resolve_futures();

    // Print results
    responses
        .filter_map(q!(|response_opt| response_opt))
        .for_each(q!(|(client_id, result)| {
            println!("📤 Result for client #{}: {}", client_id, result);
        }));

    (server, clients)
}