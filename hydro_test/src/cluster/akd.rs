use hydro_lang::*;
use akd::{AkdLabel, AkdValue, LookupProof};
use akd::storage::StorageManager;
use akd::storage::memory::AsyncInMemoryDatabase;
use akd::ecvrf::HardCodedAkdVRF;
use akd::directory::{Directory, HistoryParams};
use serde::{Serialize, Deserialize};

// type Config = akd::ExperimentalConfiguration<akd::ExampleLabel>;

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsg {
    pub updates: Vec<(AkdLabel, AkdValue)>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsgResponse {
    pub epoch: u64,
    pub root_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsgResponse {
    pub lookup_proof: LookupProof,
    pub epoch_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct HistoryMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct HistoryMsgResponse {
    pub history_proof: Vec<u8>, // Serialized HistoryProof
    pub epoch_hash: Vec<u8>,
}

// Unified request type that can handle all AKD operations
#[derive(Serialize, Deserialize, Clone, Debug)]
pub enum AkdRequest {
    Publish(PublishMsg),
    Lookup(LookupMsg),
    History(HistoryMsg),
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub enum AkdResponse {
    Publish(PublishMsgResponse),
    Lookup(LookupMsgResponse),
    History(HistoryMsgResponse),
}

pub struct Server {}

pub struct Clients {}

// API
    // Publish: publish client ID, public key
    // - label: AkdLabel (client identifier)
    // - value: AkdValue (public key data)
    // - epoch: u64 (optional version number)

    // Lookup: verify mapping
    // - label: AkdLabel (client identifier to lookup)
    // - epoch: u64 (specific version, or latest if not specified)
    // - Returns: LookupProof for verification

    // History: verify history of mappings
    // - label: AkdLabel (client identifier)
    // - history_params: HistoryParams (epoch range, limit)
    // - Returns: Vec<HistoryProof> with historical values

pub fn akd_server<'a>(flow: &FlowBuilder<'a>) -> (Process<'a, Server>, Cluster<'a, Clients>) {
    let clients = flow.cluster::<Clients>();
    let server = flow.process::<Server>();

    // Create a demonstration that shows publish + lookup working in the same AKD instance
    let demo_requests = clients
        .source_iter(q!([
            // Each client will publish data and then immediately look it up
            format!("demo_for_client_{}", CLUSTER_SELF_ID.raw_id)
        ]))
        .send_bincode(&server);

    // Process demo requests - each request will publish data and then look it up
    let responses = demo_requests
        .map(q!(|(client_id, demo_name)| async move {
            // Initialize AKD directory for this demo
            let db = AsyncInMemoryDatabase::new();
            let storage_manager = StorageManager::new_no_cache(db);
            let vrf = HardCodedAkdVRF{};

            let akd = Directory::<akd::ExperimentalConfiguration<akd::ExampleLabel>, _, _>::new(storage_manager, vrf)
                .await
                .expect("Could not create AKD directory");

            let label = AkdLabel::from(format!("client_{}", client_id.raw_id).as_str());
            let value = AkdValue::from(format!("value_from_{}", demo_name).as_str());

            println!("=== Demo for {} ===", demo_name);

            // Step 1: Publish data
            println!("Step 1: Publishing data for client #{}", client_id);
            let publish_result = akd.publish(vec![(label.clone(), value.clone())]).await;

            match publish_result {
                Ok(epoch_hash) => {
                    println!("✅ Published successfully: epoch {}, hash: {:?}",
                            epoch_hash.epoch(), epoch_hash.hash());

                    // Step 2: Immediately look up the data we just published
                    println!("Step 2: Looking up the data we just published");
                    match akd.lookup(label.clone()).await {
                        Ok((lookup_proof, lookup_epoch_hash)) => {
                            println!("✅ Lookup successful! Found data for client #{}", client_id);
                            println!("   Proof: {:?}", lookup_proof);
                            println!("   Epoch hash: {:?}", lookup_epoch_hash.hash());

                            // Step 3: Get history for the key
                            println!("Step 3: Getting history for the key");
                            match akd.key_history(&label, HistoryParams::Complete).await {
                                Ok((history_proof, history_epoch_hash)) => {
                                    println!("✅ History lookup successful! Got history proof");
                                    println!("   History epoch hash: {:?}", history_epoch_hash.hash());
                                    Some(format!("✅ Complete demo successful for {}", demo_name))
                                },
                                Err(e) => {
                                    println!("❌ History lookup failed: {:?}", e);
                                    Some(format!("⚠️  Demo partially successful for {} (publish + lookup worked, history failed)", demo_name))
                                }
                            }
                        },
                        Err(e) => {
                            println!("❌ Lookup failed: {:?}", e);
                            Some(format!("⚠️  Demo partially successful for {} (publish worked, lookup failed)", demo_name))
                        }
                    }
                },
                Err(e) => {
                    println!("❌ Publish failed: {:?}", e);
                    Some(format!("❌ Demo failed for {}", demo_name))
                }
            }
        }))
        .resolve_futures();

    // Print demo results
    responses
        .filter_map(q!(|response_opt| response_opt))
        .for_each(q!(|result| {
            println!("Demo result: {}", result);
        }));

    (server, clients)
}