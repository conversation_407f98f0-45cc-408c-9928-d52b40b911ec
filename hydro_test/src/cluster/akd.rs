use hydro_lang::*;
use akd::{AkdLabel, AkdValue, LookupProof};
use akd::storage::StorageManager;
use akd::storage::memory::AsyncInMemoryDatabase;
use akd::ecvrf::HardCodedAkdVRF;
use akd::directory::Directory;
use serde::{Serialize, Deserialize};

type Config = akd::ExperimentalConfiguration<akd::ExampleLabel>;

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsg {
    pub updates: Vec<(AkdLabel, AkdValue)>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsgResponse {
    pub epoch: u64,
    pub root_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsgResponse {
    pub lookup_proof: LookupProof,
    pub epoch_hash: Vec<u8>,
}

pub struct Server {}

pub struct Clients {}

// API
    // Publish: publish client ID, public key
    // - label: AkdLabel (client identifier)
    // - value: AkdValue (public key data)
    // - epoch: u64 (optional version number)

    // Lookup: verify mapping
    // - label: AkdLabel (client identifier to lookup)
    // - epoch: u64 (specific version, or latest if not specified)
    // - Returns: LookupProof for verification

    // History: verify history of mappings
    // - label: AkdLabel (client identifier)
    // - history_params: HistoryParams (epoch range, limit)
    // - Returns: Vec<HistoryProof> with historical values

pub fn akd_server<'a>(flow: &FlowBuilder<'a>) -> (Process<'a, Server>, Cluster<'a, Clients>) {
    // generate clients and commands
    let clients = flow.cluster::<Clients>();
    // Assume single server.
    let server = flow.process::<Server>();

    // Handle client publish requests and initialize AKD on demand
    let client_publish_req = clients
        .source_iter(q!([PublishMsg {
            updates: vec![(
                AkdLabel::from(format!("{}", CLUSTER_SELF_ID.raw_id).as_str()),
                AkdValue::from("AAABBBCCC"),
            )],
        }]))
        .send_bincode(&server);

    // Process publish requests with async AKD operations
    client_publish_req
        .map(q!(|(client_id, msg)| async move {
            // Initialize AKD directory at runtime
            let db = AsyncInMemoryDatabase::new();
            let storage_manager = StorageManager::new_no_cache(db);
            let vrf = HardCodedAkdVRF{};

            let mut akd = Directory::<Config, _, _>::new(storage_manager, vrf)
                .await
                .expect("Could not create a new directory");

            // Process the publish request
            println!("Processing publish request from client #{}: {:?}", client_id, msg);

            // TODO: Actually publish the updates to AKD
            // let result = akd.publish(msg.updates).await.expect("Failed to publish");
            // println!("Published epoch: {}", result.epoch());

            (client_id, msg)
        }))
        .resolve_futures()
        .for_each(q!(|(client_id, msg)| {
            println!("Completed processing for client #{}: {:?}", client_id, msg);
        }));

    (server, clients)
}