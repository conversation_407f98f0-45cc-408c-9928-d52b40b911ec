use hydro_lang::*;
use akd::{AkdLabel, AkdValue, LookupProof};
use akd::storage::StorageManager;
use akd::storage::memory::AsyncInMemoryDatabase;
use akd::ecvrf::HardCodedAkdVRF;
use akd::directory::Directory;
use serde::{Serialize, Deserialize};

type Config = akd::WhatsAppV1Configuration;

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsg {
    pub updates: Vec<(AkdLabel, AkdValue)>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct PublishMsgResponse {
    pub epoch: u64,
    pub root_hash: Vec<u8>,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsg {
    pub label: AkdLabel,
}

#[derive(Serialize, Deserialize, Clone, Debug)]
pub struct LookupMsgResponse {
    pub lookup_proof: LookupProof,
    pub epoch_hash: Vec<u8>,
}

pub struct Server {}

pub struct Clients {}

// API
    // Publish: publish client ID, public key
    // - label: AkdLabel (client identifier)
    // - value: AkdValue (public key data)
    // - epoch: u64 (optional version number)

    // Lookup: verify mapping
    // - label: AkdLabel (client identifier to lookup)
    // - epoch: u64 (specific version, or latest if not specified)
    // - Returns: LookupProof for verification

    // History: verify history of mappings
    // - label: AkdLabel (client identifier)
    // - history_params: HistoryParams (epoch range, limit)
    // - Returns: Vec<HistoryProof> with historical values

pub fn akd_server<'a>(flow: &FlowBuilder<'a>) -> (Process<'a, Server>, Cluster<'a, Clients>) {
    // generate clients and commands
    let clients = flow.cluster::<Clients>();
    // Assume single server.
    let server = flow.process::<Server>();

    let db = AsyncInMemoryDatabase::new();
    let storage_manager = StorageManager::new_no_cache(db);
    let vrf = HardCodedAkdVRF{};

    let mut akd = Directory::<Config, _, _>::new(storage_manager, vrf)
    .await
    .expect("Could not create a new directory");

    // let client_publish_req = clients
    //     .source_iter(q!([PublishMsg {
    //         updates: vec![(
    //             AkdLabel::from(format!("{}", CLUSTER_SELF_ID.raw_id).as_str()),
    //             AkdValue::from("AAABBBCCC"),
    //         )],
    //     }]))
    //     .send_bincode(&server)
    //     .inspect(q!(|(id, msg)| println!(
    //         "...publishing {} entries from client #{}...",
    //         msg.updates.len(), id
    //     )));

    // let server_publish_resp = client_publish_req.send_bincode(&server);

    // let akd_state = server_publish_resp.fold_keyed(q!(|| 0), q!(|count, _| *count += 1));

    // server_publish_resp
    //     .for_each(q!(|(id, msg)| println!(
    //         "Received publish request from client #{}: {:?}",
    //         id, msg
    //     )));

    (server, clients)
}