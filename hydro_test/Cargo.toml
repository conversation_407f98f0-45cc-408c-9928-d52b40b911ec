[package]
name = "hydro_test"
publish = false
version = "0.0.0"
edition = "2024"

[lints]
workspace = true

[features]
default = ["stageleft_devel"]
stageleft_devel = []

[dependencies]
hydro_lang = { path = "../hydro_lang", version = "^0.13.2" }
hydro_std = { path = "../hydro_std", version = "^0.13.0" }
stageleft = "0.8.1"
rand = "0.8.0"
serde = { version = "1.0.197", features = ["derive"] }
ctor = "0.2.9"
colored = "3.0.0"
palette = "0.7.6"
akd = { version = "0.11.0", features = ["serde_serialization", "experimental"] }

[build-dependencies]
stageleft_tool = "0.8.1"

[dev-dependencies]
ctor = "0.2"
dfir_lang = { path = "../dfir_lang", version = "^0.13.0" }
futures = "0.3.0"
hydro_deploy = { path = "../hydro_deploy/core", version = "^0.13.0" }
hydro_lang = { path = "../hydro_lang", version = "^0.13.2", features = [
    "deploy",
] }
include_mdtests = { path = "../include_mdtests", version = "^0.0.0" }
insta = "1.39"
tokio = { version = "1.29.0", features = ["full"] }
tokio-test = "0.4.4"
